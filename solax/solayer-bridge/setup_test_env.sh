#!/bin/bash

# Source environment to get Solana tools
source ~/.bashrc

echo "🚀 Setting up test environment for out-of-bounds vulnerability POC"

mkdir -p target/idl
mkdir -p target/deploy
mkdir -p tests/

echo "📦 Checking Anchor version..."
anchor --version

echo "📥 Downloading IDL from devnet..."
anchor idl fetch 6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg --out target/idl/bridge_program.json

echo "📥 Cloning bridge program from devnet..."
solana program dump 6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg --url devnet target/deploy/bridge.so
chmod +x target/deploy/bridge.so

echo "📥 Cloning Metaplex program from devnet..."
solana program dump metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s --url devnet target/deploy/metaplex.so
chmod +x target/deploy/metaplex.so

echo "📋 Copying test files..."
cp -r scripts/constants.ts tests/constants.ts
cp -r scripts/utils.ts tests/utils.ts
cp -r scripts/types.ts tests/types.ts
cp -r scripts/devnet/solana/initialize.ts tests/initialize.ts

echo "✅ Setup complete! Next steps:"
echo "1. Modify Anchor.toml as shown in setup.md"
echo "2. Modify tests/initialize.ts as shown in setup.md"
echo "3. Run 'npm i' to install dependencies"
echo "4. Run 'anchor test --skip-build' to test"
