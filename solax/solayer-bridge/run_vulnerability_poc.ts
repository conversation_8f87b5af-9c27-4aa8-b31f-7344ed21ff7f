#!/usr/bin/env ts-node

/**
 * VULNERABILITY POC RUNNER
 * 
 * This script runs the out-of-bounds vulnerability POC and provides
 * a comprehensive analysis of the security issue.
 */

import { OutOfBoundsVulnerabilityPOC } from "./out_of_bounds_vulnerability_poc";

async function main() {
  console.log("🚀 STARTING OUT-OF-BOUNDS VULNERABILITY POC");
  console.log("=" .repeat(80));
  console.log("TESTING: verify_signature.rs array bounds vulnerability");
  console.log("LOCATION: programs/bridge/src/contexts/verify_signature.rs:76");
  console.log("ISSUE: Missing bounds check for signer_indexes array access");
  console.log("=" .repeat(80));

  try {
    const poc = new OutOfBoundsVulnerabilityPOC();
    await poc.runVulnerabilityAnalysis();
  } catch (error) {
    console.error("❌ POC execution failed:", error);
    process.exit(1);
  }

  console.log("\n✅ POC execution completed");
}

if (require.main === module) {
  main().catch((error) => {
    console.error("Fatal error:", error);
    process.exit(1);
  });
}
