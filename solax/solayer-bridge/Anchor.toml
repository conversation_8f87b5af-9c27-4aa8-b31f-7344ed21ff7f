[toolchain]

[features]
seeds = false
skip-lint = false

[programs.localnet]
bridge_program = "6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "localnet"
wallet = "./keys/devnet/solana_manager.json"

[[test.genesis]]
address = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
program = "target/deploy/metaplex.so"

[[test.genesis]]
address = "6kpxYKjqe8z66hnDHbbjhEUxha46cnz2UqrneGECmFBg"
program = "target/deploy/bridge.so"

[test]
startup_wait = 10000

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
