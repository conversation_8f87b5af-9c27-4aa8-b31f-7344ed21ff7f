What’s wrong



Possible stale-owner check after associated_token::create CPI (Account reloading pitfall) (Medium / DoS)

Where: init_if_needed_and_check_bridge_handler_vault

let owner_program = self.bridge_handler_vault.to_account_info().owner;
if owner_program == self.system_program.key { /* create ATA */ }
// ... later:
if owner_program != self.token_program.key { return Err(...ConstraintAssociatedTokenTokenProgram) }


owner_program is captured before the CPI that creates the ATA. After CPI, Anchor does not auto-refresh deserialized accounts; relying on the pre-CPI owner can be stale and cause false failures. This is the classic <PERSON>chor “reload” foot-gun. 
zellic.io
Helius

Exploit angle: In the branch where the ATA is just created, the owner should now be the Token program; using the pre-CPI snapshot may still read system_program, tripping the constraint and bricking first-time calls.

Patch: Re-fetch the owner after the CPI (or stop caching it).

// Move this after the CPI block:
let owner_program_after = self.bridge_handler_vault.to_account_info().owner;
require_keys_eq!(*owner_program_after, self.token_program.key(),
    anchor_lang::error::ErrorCode::ConstraintAssociatedTokenTokenProgram);


You already manually re-deserialize TokenAccount (which is fresh), so this change makes the owner check consistent. (General rule: reload/refresh account data after CPI before using it again. ) 
zellic.io
Helius
You snapshot owner_program before calling associated_token::create.

If the ATA didn’t exist, that snapshot will be system_program.

After the CPI, the ATA’s owner becomes token_program, but your later check still uses the stale pre-CPI value, triggering ConstraintAssociatedTokenTokenProgram on first-time calls (DoS for first bridge on a mint).

You already re-deserialize the TokenAccount data fresh, but the program owner check uses the stale owner_program.

Minimal safe patch

Don’t cache the owner across the CPI. Re-read it after the create:

#[allow(clippy::too_many_arguments)]
fn init_if_needed_and_check_bridge_handler_vault(&mut self) -> Result<()> {
    let rent = Rent::get()?;

    // Decide whether we need to create
    let needs_init = self.bridge_handler_vault.to_account_info().owner == self.system_program.key;

    if needs_init {
        // Create the ATA
        let cpi_program = self.associated_token_program.to_account_info();
        let cpi_accounts = ::anchor_spl::associated_token::Create {
            payer: self.signer.to_account_info(),
            associated_token: self.bridge_handler_vault.to_account_info(),
            authority: self.bridge_handler.to_account_info(),
            mint: self.mint.to_account_info(),
            system_program: self.system_program.to_account_info(),
            token_program: self.token_program.to_account_info(),
        };
        let cpi_ctx = anchor_lang::context::CpiContext::new(cpi_program, cpi_accounts);
        ::anchor_spl::associated_token::create(cpi_ctx)?;
    }

    // 🔁 RE-FETCH owner after potential CPI side effects
    let owner_program_after = self.bridge_handler_vault.to_account_info().owner;
    require_keys_eq!(
        *owner_program_after,
        self.token_program.key(),
        anchor_lang::error::ErrorCode::ConstraintAssociatedTokenTokenProgram
    );

    // Freshly read account data (you already do this)
    {
        let data = self.bridge_handler_vault.to_account_info().data.borrow().to_vec();
        let token_account = TokenAccount::try_deserialize(&mut data.as_slice())
            .map_err(|e| e.with_account_name("bridge_handler_vault"))?;

        if token_account.mint != self.mint.key() {
            return Err(anchor_lang::error::Error::from(
                anchor_lang::error::ErrorCode::ConstraintTokenMint,
            )
            .with_account_name("bridge_handler_vault")
            .with_pubkeys((token_account.mint, self.mint.key())));
        }

        if token_account.owner != self.bridge_handler.key() {
            return Err(anchor_lang::error::Error::from(
                anchor_lang::error::ErrorCode::ConstraintTokenOwner,
            )
            .with_account_name("bridge_handler_vault")
            .with_pubkeys((token_account.owner, self.bridge_handler.key())));
        }

        if self.bridge_handler_vault.key()
            != ::anchor_spl::associated_token::get_associated_token_address_with_program_id(
                &self.bridge_handler.key(),
                &self.mint.key(),
                &self.token_program.key(),
            )
        {
            return Err(anchor_lang::error::Error::from(
                anchor_lang::error::ErrorCode::AccountNotAssociatedTokenAccount,
            )
            .with_account_name("bridge_handler_vault"));
        }
    }

    // Rent-exempt check will now read updated lamports/size
    require!(
        rent.is_exempt(
            self.bridge_handler_vault.to_account_info().lamports(),
            self.bridge_handler_vault.to_account_info().try_data_len()?,
        ),
        anchor_lang::error::ErrorCode::ConstraintRentExempt
    );

    Ok(())
}

Alternative patterns (also safe)

Use InterfaceAccount<'info, TokenAccount> for the vault and call .reload()? after the CPI (if stack limits allow), then run your checks.

Or just always read self.bridge_handler_vault.to_account_info().owner after any CPI that could change it; never cache it across CPIs.

Impact

Without this fix, any first-time bridge_handler_vault creation path will error out (false failure), effectively DoSing initial bridges for a mint. After creation, subsequent calls pass.

This change aligns your owner check with the already-fresh token-account deserialization and removes the stale-owner hazard.